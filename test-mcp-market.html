<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP市场页面预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@iconify/iconify@3.1.1/dist/iconify.js"></script>
    <style>
        .line-clamp-1 {
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }
        .line-clamp-3 {
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <div class="w-full min-h-screen bg-background">
        <div class="max-w-7xl mx-auto">
            <!-- 页面标题栏 -->
            <div class="px-6 py-6 border-b bg-white/30">
                <div class="flex items-center gap-3">
                    <div class="flex items-center gap-2">
                        <iconify-icon icon="lucide:shopping-bag" class="w-5 h-5 text-blue-600"></iconify-icon>
                        <h1 class="text-lg font-semibold text-gray-900">MCP内置市场</h1>
                    </div>
                    <div class="flex items-center gap-2 text-xs text-gray-500">
                        <span>•</span>
                        <a href="https://mcprouter.co/" target="_blank" class="hover:text-gray-700 transition-colors underline-offset-4 hover:underline">
                            由 MCPRouter 提供支持
                        </a>
                    </div>
                </div>
            </div>
            <!-- MCP全局开关 -->
            <div class="mx-6 mt-6 p-4 bg-white rounded-lg border shadow-sm">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center gap-2 mb-1">
                            <iconify-icon icon="lucide:power" class="w-4 h-4 text-blue-600"></iconify-icon>
                            <h3 class="text-sm font-semibold text-gray-900">启用MCP功能</h3>
                        </div>
                        <p class="text-xs text-gray-600">
                            启用后可以使用MCP服务器扩展功能
                        </p>
                    </div>
                    <div class="flex items-center gap-3">
                        <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded">已启用</span>
                        <div class="w-10 h-6 bg-blue-600 rounded-full relative">
                            <div class="w-4 h-4 bg-white rounded-full absolute top-1 right-1 transition-all"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- NPM源配置区域 -->
            <div class="mx-6 mt-4 p-4 bg-white rounded-lg border shadow-sm">
                <div class="flex items-center gap-2 mb-4">
                    <iconify-icon icon="lucide:package" class="w-4 h-4 text-blue-600"></iconify-icon>
                    <h4 class="text-sm font-semibold text-gray-900">NPM源配置</h4>
                </div>
                <div class="space-y-4">
                    <!-- 当前源状态 -->
                    <div class="flex items-center justify-between bg-gray-50 rounded-lg p-3 border">
                        <div class="flex items-center gap-3">
                            <iconify-icon icon="lucide:globe" class="w-4 h-4 text-gray-500"></iconify-icon>
                            <div>
                                <div class="text-xs text-gray-500 mb-1">当前NPM源</div>
                                <div class="text-sm font-mono text-gray-900">https://registry.npmjs.org/</div>
                                <div class="text-xs text-gray-500 mt-1">
                                    最后检查: 刚刚 <span class="ml-1 text-xs bg-gray-200 text-gray-700 px-1 rounded">缓存</span>
                                </div>
                            </div>
                        </div>
                        <button class="px-3 py-1 text-sm border rounded hover:bg-gray-50 flex items-center gap-1">
                            <iconify-icon icon="lucide:refresh-cw" class="w-3.5 h-3.5"></iconify-icon>
                            刷新
                        </button>
                    </div>

                    <!-- 自动检测开关 -->
                    <div class="flex items-center justify-between p-3 bg-gray-25 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-gray-900">自动检测NPM源</div>
                            <div class="text-xs text-gray-600 mt-1">自动检测并使用最快的NPM源</div>
                        </div>
                        <div class="w-10 h-6 bg-blue-600 rounded-full relative">
                            <div class="w-4 h-4 bg-white rounded-full absolute top-1 right-1 transition-all"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 外部MCP市场入口 -->
            <div class="mx-6 mt-4 p-4 bg-white rounded-lg border shadow-sm">
                <div class="flex items-center gap-2 mb-4">
                    <iconify-icon icon="lucide:external-link" class="w-4 h-4 text-blue-600"></iconify-icon>
                    <h4 class="text-sm font-semibold text-gray-900">外部MCP市场</h4>
                </div>
                <div class="space-y-3">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <button class="h-auto p-3 flex flex-col items-center gap-2 border rounded-lg hover:bg-gray-50 transition-colors">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIxIDEyVjdIMTlWNUMxOSAzLjkgMTguMSAzIDE3IDNIOUMzLjkgMyAzIDMuOSAzIDVWMTlDMyAyMC4xIDMuOSAyMSA1IDIxSDEyVjE5SDVWNUgxN1Y3SDE5VjEySDIxWiIgZmlsbD0iY3VycmVudENvbG9yIi8+CjxwYXRoIGQ9Ik0xNiAxNEgxOFYxNkgxNlYxNFoiIGZpbGw9ImN1cnJlbnRDb2xvciIvPgo8cGF0aCBkPSJNMjAgMTRIMjJWMTZIMjBWMTRaIiBmaWxsPSJjdXJyZW50Q29sb3IiLz4KPHN2Zz4K" class="w-5 h-5" />
                            <span class="text-sm font-medium">Higress MCP市场</span>
                            <iconify-icon icon="lucide:external-link" class="w-3 h-3 text-gray-500"></iconify-icon>
                        </button>
                    </div>
                    <div class="text-xs text-gray-600 bg-blue-50 p-2 rounded">
                        访问外部MCP市场获取更多服务器
                    </div>
                </div>
            </div>

            <!-- MCP服务器列表 -->
            <div class="mx-6 mt-4 bg-white rounded-lg border shadow-sm">
                <div class="p-4 border-b bg-gray-50">
                    <div class="flex items-center gap-2">
                        <iconify-icon icon="lucide:grid-3x3" class="w-4 h-4 text-blue-600"></iconify-icon>
                        <h4 class="text-sm font-semibold text-gray-900">可用的MCP服务器</h4>
                        <span class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">12 个</span>
                    </div>
                </div>

                <div>
                    <div class="p-4 grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
                        <!-- 示例MCP服务器卡片 -->
                        <div class="group border rounded-lg p-4 bg-white hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 flex flex-col shadow-sm hover:shadow-md">
                            <div class="text-xs text-gray-500 mb-2">OpenAI</div>
                            <div class="text-sm font-semibold mb-2 line-clamp-1 group-hover:text-blue-600 transition-colors">
                                文件系统工具
                            </div>
                            <div class="text-xs text-gray-600 line-clamp-3 flex-1 mb-3">
                                提供文件系统操作功能，包括读取、写入、创建和删除文件等操作
                            </div>
                            <div class="flex items-center justify-between gap-2">
                                <span class="text-xs font-mono px-2 py-1 bg-gray-100 rounded text-gray-600">filesystem</span>
                                <button class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors flex items-center gap-1">
                                    <iconify-icon icon="lucide:download" class="w-3.5 h-3.5"></iconify-icon>
                                    安装
                                </button>
                            </div>
                        </div>

                        <div class="group border rounded-lg p-4 bg-white hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 flex flex-col shadow-sm hover:shadow-md">
                            <div class="text-xs text-gray-500 mb-2">GitHub</div>
                            <div class="text-sm font-semibold mb-2 line-clamp-1 group-hover:text-blue-600 transition-colors">
                                GitHub集成
                            </div>
                            <div class="text-xs text-gray-600 line-clamp-3 flex-1 mb-3">
                                与GitHub API集成，提供仓库管理、问题跟踪等功能
                            </div>
                            <div class="flex items-center justify-between gap-2">
                                <span class="text-xs font-mono px-2 py-1 bg-gray-100 rounded text-gray-600">github</span>
                                <button class="px-3 py-1 text-xs bg-gray-400 text-white rounded cursor-not-allowed flex items-center gap-1">
                                    <iconify-icon icon="lucide:check" class="w-3.5 h-3.5"></iconify-icon>
                                    已安装
                                </button>
                            </div>
                        </div>

                        <div class="group border rounded-lg p-4 bg-white hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 flex flex-col shadow-sm hover:shadow-md">
                            <div class="text-xs text-gray-500 mb-2">Anthropic</div>
                            <div class="text-sm font-semibold mb-2 line-clamp-1 group-hover:text-blue-600 transition-colors">
                                数据库连接器
                            </div>
                            <div class="text-xs text-gray-600 line-clamp-3 flex-1 mb-3">
                                连接各种数据库，执行查询和数据操作
                            </div>
                            <div class="flex items-center justify-between gap-2">
                                <span class="text-xs font-mono px-2 py-1 bg-gray-100 rounded text-gray-600">database</span>
                                <button class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors flex items-center gap-1">
                                    <iconify-icon icon="lucide:download" class="w-3.5 h-3.5"></iconify-icon>
                                    安装
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API密钥配置区域 - 放在底部 -->
            <div class="mx-6 mt-4 mb-6 p-4 bg-white rounded-lg border shadow-sm">
                <div class="flex items-center gap-2 mb-3">
                    <iconify-icon icon="lucide:key" class="w-4 h-4 text-blue-600"></iconify-icon>
                    <h4 class="text-sm font-semibold text-gray-900">API密钥配置</h4>
                </div>
                
                <!-- API Key 获取提示 -->
                <div class="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="text-xs text-blue-700">
                        需要API密钥才能安装MCP服务器。
                        <button class="text-blue-600 hover:underline font-medium">
                            获取密钥指南
                        </button>
                        查看如何获取。
                    </div>
                </div>

                <!-- 密钥输入框 -->
                <div class="flex items-center gap-3">
                    <div class="flex-1">
                        <input
                            type="password"
                            placeholder="请输入您的API密钥"
                            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
                        <iconify-icon icon="lucide:save" class="w-4 h-4"></iconify-icon>
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
